{% extends "base.html" %}

{% block title %}Advertising - Shopify AI Manager{% endblock %}

{% block page_title %}Advertising Management{% endblock %}
{% block page_subtitle %}Create and optimize ad campaigns with AI assistance{% endblock %}

{% block content %}
<div class="row">
    <!-- Ads Chat Assistant -->
    <div class="col-lg-8 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-robot"></i> Advertising AI Assistant
                </h5>
            </div>
            <div class="card-body">
                <div class="chat-container">
                    <div class="chat-messages" id="ads-chat-messages">
                        <div class="message ai">
                            <i class="fas fa-bullhorn"></i> 
                            <strong>Ads Assistant:</strong> Hello! I'm your advertising expert. I can help you with:
                            <ul class="mt-2 mb-0">
                                <li>Google Ads and Facebook Ads campaign creation</li>
                                <li>Ad copy writing and optimization</li>
                                <li>Audience targeting strategies</li>
                                <li>Budget allocation and bidding strategies</li>
                                <li>Performance analysis and optimization</li>
                                <li>Product listing ads for shopping campaigns</li>
                            </ul>
                            What advertising challenge can I help you solve today?
                        </div>
                    </div>
                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control" id="ads-chat-input" 
                                   placeholder="Ask about advertising strategies...">
                            <button class="btn btn-shopify" type="button" onclick="sendAdsMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Ads Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Ad Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="quickAdsAction('campaign')">
                        <i class="fas fa-plus"></i> Create Campaign
                    </button>
                    <button class="btn btn-outline-success" onclick="quickAdsAction('copy')">
                        <i class="fas fa-edit"></i> Generate Ad Copy
                    </button>
                    <button class="btn btn-outline-info" onclick="quickAdsAction('audience')">
                        <i class="fas fa-users"></i> Audience Research
                    </button>
                    <button class="btn btn-outline-warning" onclick="quickAdsAction('optimize')">
                        <i class="fas fa-chart-line"></i> Optimize Performance
                    </button>
                    <button class="btn btn-outline-secondary" onclick="quickAdsAction('budget')">
                        <i class="fas fa-dollar-sign"></i> Budget Planning
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Campaign Performance -->
        <div class="card module-card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Campaign Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-primary mb-1">Active Campaigns</h6>
                            <h4 class="mb-0">3</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-success mb-1">Total Spend</h6>
                            <h4 class="mb-0">$1,247</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-info mb-1">ROAS</h6>
                            <h4 class="mb-0">3.2x</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-warning mb-1">Conversions</h6>
                            <h4 class="mb-0">89</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Ad Campaign Builder -->
    <div class="col-12 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-magic"></i> AI Campaign Builder
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Campaign Details</h6>
                        <div class="mb-3">
                            <label class="form-label">Campaign Type</label>
                            <select class="form-select" id="campaign-type">
                                <option value="search">Search Ads</option>
                                <option value="shopping">Shopping Ads</option>
                                <option value="display">Display Ads</option>
                                <option value="social">Social Media Ads</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Target Product/Category</label>
                            <input type="text" class="form-control" id="target-product" 
                                   placeholder="e.g., Wireless Headphones">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Budget Range</label>
                            <select class="form-select" id="budget-range">
                                <option value="low">$10-50/day</option>
                                <option value="medium">$50-200/day</option>
                                <option value="high">$200-500/day</option>
                                <option value="premium">$500+/day</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Target Audience</h6>
                        <div class="mb-3">
                            <label class="form-label">Age Range</label>
                            <select class="form-select" id="age-range">
                                <option value="18-24">18-24</option>
                                <option value="25-34">25-34</option>
                                <option value="35-44">35-44</option>
                                <option value="45-54">45-54</option>
                                <option value="55+">55+</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Interests</label>
                            <input type="text" class="form-control" id="interests" 
                                   placeholder="e.g., technology, music, fitness">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Geographic Location</label>
                            <input type="text" class="form-control" id="location" 
                                   placeholder="e.g., United States, Canada">
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-shopify" onclick="generateCampaign()">
                        <i class="fas fa-magic"></i> Generate Campaign with AI
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Active Campaigns -->
    <div class="col-12">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Active Campaigns
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Campaign Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Spend</th>
                                <th>ROAS</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <strong>Wireless Headphones - Search</strong><br>
                                    <small class="text-muted">Google Ads</small>
                                </td>
                                <td><span class="badge bg-primary">Search</span></td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>$456.78</td>
                                <td>3.4x</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="optimizeCampaign('campaign-1')">
                                        <i class="fas fa-chart-line"></i> Optimize
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Summer Collection - Shopping</strong><br>
                                    <small class="text-muted">Google Shopping</small>
                                </td>
                                <td><span class="badge bg-info">Shopping</span></td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>$623.45</td>
                                <td>2.8x</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="optimizeCampaign('campaign-2')">
                                        <i class="fas fa-chart-line"></i> Optimize
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Brand Awareness - Display</strong><br>
                                    <small class="text-muted">Facebook Ads</small>
                                </td>
                                <td><span class="badge bg-warning">Display</span></td>
                                <td><span class="badge bg-warning">Paused</span></td>
                                <td>$167.23</td>
                                <td>1.9x</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-success" onclick="resumeCampaign('campaign-3')">
                                        <i class="fas fa-play"></i> Resume
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campaign Generation Modal -->
<div class="modal fade" id="campaignModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">AI-Generated Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="campaignContent">
                <!-- Generated campaign content will appear here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-shopify">Export Campaign</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Send Ads message
    function sendAdsMessage() {
        const input = document.getElementById('ads-chat-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        const chatContainer = document.querySelector('.chat-container');
        sendAIMessage(message, 'ads', chatContainer);
        
        input.value = '';
    }
    
    // Handle Enter key in chat input
    document.getElementById('ads-chat-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendAdsMessage();
        }
    });
    
    // Quick ads actions
    function quickAdsAction(action) {
        const prompts = {
            'campaign': 'Help me create a new advertising campaign for my Shopify store. Suggest campaign structure, targeting, and budget allocation.',
            'copy': 'Generate compelling ad copy for my products. Create headlines, descriptions, and call-to-action text that converts.',
            'audience': 'Research and suggest target audiences for my advertising campaigns. Analyze demographics, interests, and behaviors.',
            'optimize': 'Analyze my current ad performance and provide optimization recommendations to improve ROAS and reduce costs.',
            'budget': 'Help me plan and allocate my advertising budget across different platforms and campaigns for maximum ROI.'
        };
        
        const input = document.getElementById('ads-chat-input');
        input.value = prompts[action];
        sendAdsMessage();
    }
    
    // Generate campaign with AI
    function generateCampaign() {
        const campaignType = document.getElementById('campaign-type').value;
        const targetProduct = document.getElementById('target-product').value;
        const budgetRange = document.getElementById('budget-range').value;
        const ageRange = document.getElementById('age-range').value;
        const interests = document.getElementById('interests').value;
        const location = document.getElementById('location').value;
        
        if (!targetProduct) {
            alert('Please enter a target product or category');
            return;
        }
        
        const modal = new bootstrap.Modal(document.getElementById('campaignModal'));
        const content = document.getElementById('campaignContent');
        
        // Show loading
        content.innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">AI is generating your campaign...</p>
            </div>
        `;
        
        modal.show();
        
        // Generate campaign with AI
        const prompt = `Create a comprehensive ${campaignType} advertising campaign for "${targetProduct}". 
        Target audience: Age ${ageRange}, interests: ${interests}, location: ${location}. 
        Budget: ${budgetRange}. 
        Include campaign structure, ad copy, targeting settings, and optimization recommendations.`;
        
        fetch('/api/ai-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: prompt,
                module: 'ads'
            })
        })
        .then(response => response.json())
        .then(data => {
            content.innerHTML = `
                <div class="campaign-result">
                    <h6>Generated Campaign Strategy:</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.response.replace(/\n/g, '<br>')}
                    </div>
                </div>
            `;
        })
        .catch(error => {
            content.innerHTML = `<div class="alert alert-danger">Error generating campaign: ${error.message}</div>`;
        });
    }
    
    // Optimize campaign
    function optimizeCampaign(campaignId) {
        const input = document.getElementById('ads-chat-input');
        input.value = `Analyze and optimize campaign ${campaignId}. Provide specific recommendations for improving performance, reducing costs, and increasing ROAS.`;
        sendAdsMessage();
    }
    
    // Resume campaign
    function resumeCampaign(campaignId) {
        alert(`Campaign ${campaignId} has been resumed. AI recommendations for reactivation have been noted.`);
    }
    
    // Initialize Ads module
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Ads module initialized');
    });
</script>
{% endblock %}
