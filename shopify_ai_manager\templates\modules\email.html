{% extends "base.html" %}

{% block title %}Email Marketing - Shopify AI Manager{% endblock %}

{% block page_title %}Email Marketing{% endblock %}
{% block page_subtitle %}Design effective email campaigns with AI assistance{% endblock %}

{% block content %}
<div class="row">
    <!-- <PERSON><PERSON> Chat Assistant -->
    <div class="col-lg-8 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-robot"></i> Email Marketing AI Assistant
                </h5>
            </div>
            <div class="card-body">
                <div class="chat-container">
                    <div class="chat-messages" id="email-chat-messages">
                        <div class="message ai">
                            <i class="fas fa-envelope"></i> 
                            <strong>Email Assistant:</strong> Hello! I'm your email marketing specialist. I can help you with:
                            <ul class="mt-2 mb-0">
                                <li>Email campaign creation and design</li>
                                <li>Subject line optimization</li>
                                <li>Automation workflow setup</li>
                                <li>Segmentation strategies</li>
                                <li>A/B testing recommendations</li>
                                <li>Deliverability optimization</li>
                                <li>Performance analysis and improvements</li>
                            </ul>
                            What email marketing goal would you like to achieve today?
                        </div>
                    </div>
                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control" id="email-chat-input" 
                                   placeholder="Ask about email marketing strategies...">
                            <button class="btn btn-shopify" type="button" onclick="sendEmailMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Email Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Email Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="quickEmailAction('campaign')">
                        <i class="fas fa-plus"></i> Create Campaign
                    </button>
                    <button class="btn btn-outline-success" onclick="quickEmailAction('subject')">
                        <i class="fas fa-edit"></i> Subject Lines
                    </button>
                    <button class="btn btn-outline-info" onclick="quickEmailAction('automation')">
                        <i class="fas fa-cogs"></i> Setup Automation
                    </button>
                    <button class="btn btn-outline-warning" onclick="quickEmailAction('segment')">
                        <i class="fas fa-users"></i> Segment Audience
                    </button>
                    <button class="btn btn-outline-secondary" onclick="quickEmailAction('analyze')">
                        <i class="fas fa-chart-line"></i> Analyze Performance
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Email Stats -->
        <div class="card module-card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Email Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-primary mb-1">Open Rate</h6>
                            <h4 class="mb-0">24.5%</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-success mb-1">Click Rate</h6>
                            <h4 class="mb-0">3.8%</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-info mb-1">Subscribers</h6>
                            <h4 class="mb-0">2,847</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-warning mb-1">Revenue</h6>
                            <h4 class="mb-0">$3,421</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Email Campaign Builder -->
    <div class="col-12 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-magic"></i> AI Email Campaign Builder
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Campaign Details</h6>
                        <div class="mb-3">
                            <label class="form-label">Campaign Type</label>
                            <select class="form-select" id="email-campaign-type">
                                <option value="newsletter">Newsletter</option>
                                <option value="promotional">Promotional</option>
                                <option value="welcome">Welcome Series</option>
                                <option value="abandoned-cart">Abandoned Cart</option>
                                <option value="product-launch">Product Launch</option>
                                <option value="seasonal">Seasonal Campaign</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Target Product/Topic</label>
                            <input type="text" class="form-control" id="email-topic" 
                                   placeholder="e.g., New Summer Collection">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Campaign Goal</label>
                            <select class="form-select" id="email-goal">
                                <option value="sales">Increase Sales</option>
                                <option value="engagement">Boost Engagement</option>
                                <option value="retention">Customer Retention</option>
                                <option value="awareness">Brand Awareness</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Audience & Timing</h6>
                        <div class="mb-3">
                            <label class="form-label">Target Segment</label>
                            <select class="form-select" id="email-segment">
                                <option value="all">All Subscribers</option>
                                <option value="new">New Customers</option>
                                <option value="returning">Returning Customers</option>
                                <option value="vip">VIP Customers</option>
                                <option value="inactive">Inactive Subscribers</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Send Time</label>
                            <select class="form-select" id="email-timing">
                                <option value="immediate">Send Immediately</option>
                                <option value="optimal">Optimal Time (AI Suggested)</option>
                                <option value="scheduled">Schedule for Later</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tone of Voice</label>
                            <select class="form-select" id="email-tone">
                                <option value="friendly">Friendly & Casual</option>
                                <option value="professional">Professional</option>
                                <option value="urgent">Urgent & Persuasive</option>
                                <option value="luxury">Luxury & Exclusive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-shopify" onclick="generateEmailCampaign()">
                        <i class="fas fa-magic"></i> Generate Email Campaign with AI
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Campaigns -->
    <div class="col-md-8 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Recent Campaigns
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Campaign Name</th>
                                <th>Type</th>
                                <th>Sent</th>
                                <th>Open Rate</th>
                                <th>Click Rate</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <strong>Summer Sale 2024</strong><br>
                                    <small class="text-muted">Sent 2 days ago</small>
                                </td>
                                <td><span class="badge bg-warning">Promotional</span></td>
                                <td>1,247</td>
                                <td>28.3%</td>
                                <td>4.2%</td>
                                <td>$1,856</td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Weekly Newsletter #23</strong><br>
                                    <small class="text-muted">Sent 1 week ago</small>
                                </td>
                                <td><span class="badge bg-info">Newsletter</span></td>
                                <td>2,847</td>
                                <td>22.1%</td>
                                <td>3.1%</td>
                                <td>$743</td>
                            </tr>
                            <tr>
                                <td>
                                    <strong>Abandoned Cart Recovery</strong><br>
                                    <small class="text-muted">Automated</small>
                                </td>
                                <td><span class="badge bg-success">Automation</span></td>
                                <td>156</td>
                                <td>31.4%</td>
                                <td>8.7%</td>
                                <td>$822</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Automation Workflows -->
    <div class="col-md-4 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> Active Automations
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Welcome Series</strong><br>
                            <small class="text-muted">3 emails, 7 days</small>
                        </div>
                        <span class="badge bg-success">Active</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Abandoned Cart</strong><br>
                            <small class="text-muted">2 emails, 24 hours</small>
                        </div>
                        <span class="badge bg-success">Active</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Win-back Campaign</strong><br>
                            <small class="text-muted">3 emails, 30 days</small>
                        </div>
                        <span class="badge bg-warning">Paused</span>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm w-100" onclick="createAutomation()">
                        <i class="fas fa-plus"></i> Create New Automation
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Campaign Modal -->
<div class="modal fade" id="emailCampaignModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">AI-Generated Email Campaign</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="emailCampaignContent">
                <!-- Generated email content will appear here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-outline-primary">Preview Email</button>
                <button type="button" class="btn btn-shopify">Send Campaign</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Send Email message
    function sendEmailMessage() {
        const input = document.getElementById('email-chat-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        const chatContainer = document.querySelector('.chat-container');
        sendAIMessage(message, 'email', chatContainer);
        
        input.value = '';
    }
    
    // Handle Enter key in chat input
    document.getElementById('email-chat-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendEmailMessage();
        }
    });
    
    // Quick email actions
    function quickEmailAction(action) {
        const prompts = {
            'campaign': 'Help me create an effective email marketing campaign for my Shopify store. Suggest campaign structure, content, and timing.',
            'subject': 'Generate compelling email subject lines that increase open rates. Provide A/B testing variations.',
            'automation': 'Set up email automation workflows for my store including welcome series, abandoned cart, and re-engagement campaigns.',
            'segment': 'Help me segment my email list for better targeting. Suggest segmentation strategies based on customer behavior.',
            'analyze': 'Analyze my email marketing performance and provide recommendations for improving open rates, click rates, and conversions.'
        };
        
        const input = document.getElementById('email-chat-input');
        input.value = prompts[action];
        sendEmailMessage();
    }
    
    // Generate email campaign with AI
    function generateEmailCampaign() {
        const campaignType = document.getElementById('email-campaign-type').value;
        const topic = document.getElementById('email-topic').value;
        const goal = document.getElementById('email-goal').value;
        const segment = document.getElementById('email-segment').value;
        const timing = document.getElementById('email-timing').value;
        const tone = document.getElementById('email-tone').value;
        
        if (!topic) {
            alert('Please enter a topic for your email campaign');
            return;
        }
        
        const modal = new bootstrap.Modal(document.getElementById('emailCampaignModal'));
        const content = document.getElementById('emailCampaignContent');
        
        // Show loading
        content.innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">AI is creating your email campaign...</p>
            </div>
        `;
        
        modal.show();
        
        // Generate campaign with AI
        const prompt = `Create a comprehensive ${campaignType} email campaign about "${topic}". 
        Goal: ${goal}, Target: ${segment}, Timing: ${timing}, Tone: ${tone}. 
        Include subject line variations, email content, call-to-action, and optimization tips.`;
        
        fetch('/api/ai-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: prompt,
                module: 'email'
            })
        })
        .then(response => response.json())
        .then(data => {
            content.innerHTML = `
                <div class="email-campaign-result">
                    <h6>Generated Email Campaign:</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.response.replace(/\n/g, '<br>')}
                    </div>
                </div>
            `;
        })
        .catch(error => {
            content.innerHTML = `<div class="alert alert-danger">Error generating campaign: ${error.message}</div>`;
        });
    }
    
    // Create automation
    function createAutomation() {
        const input = document.getElementById('email-chat-input');
        input.value = 'Help me create a new email automation workflow. Suggest the best automation type for my store and provide the complete setup.';
        sendEmailMessage();
    }
    
    // Initialize Email module
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Email module initialized');
    });
</script>
{% endblock %}
