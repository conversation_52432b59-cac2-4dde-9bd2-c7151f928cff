#!/usr/bin/env node

/**
 * Enhanced OpenRouter integration with configurable models
 * Usage: node openrouter_enhanced.cjs [model_name]
 * Example: node openrouter_enhanced.cjs gemini
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Load configuration
const configPath = path.join(__dirname, 'openrouter_config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          reject(new Error(`Invalid JSON response: ${body}`));
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function queryOpenRouter(messages, apiKey, modelId) {
  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'X-Title': 'Shopify Development Assistant'
    }
  };

  const data = {
    model: modelId,
    messages,
    max_tokens: config.maxTokens,
    temperature: config.temperature
  };

  const response = await makeRequest('https://openrouter.ai/api/v1/chat/completions', options, data);
  
  if (response.error) {
    throw new Error(`OpenRouter API error: ${response.error.message}`);
  }

  return response.choices[0].message.content;
}

function getShopifyContext(query) {
  const shopifyContext = {
    'product': `
Shopify Product API Context:
- Products are created using the Admin GraphQL API
- Key fields: title, description, vendor, product_type, tags
- Products can have multiple variants with different prices, SKUs, inventory
- Use the productCreate mutation to create products

Example GraphQL mutation:
mutation productCreate($input: ProductInput!) {
  productCreate(input: $input) {
    product {
      id
      title
      handle
    }
    userErrors {
      field
      message
    }
  }
}
`,
    'order': `
Shopify Order API Context:
- Orders represent customer purchases
- Key fields: line_items, customer, shipping_address, billing_address
- Use the orders query to fetch orders
- Use fulfillmentCreate to fulfill orders

Example GraphQL query:
query getOrders($first: Int!) {
  orders(first: $first) {
    edges {
      node {
        id
        name
        totalPrice
        customer {
          email
        }
      }
    }
  }
}
`,
    'default': `
Shopify Development Context:
- Shopify provides Admin API, Storefront API, Partner API
- Admin API is for managing store data (products, orders, customers)
- Use GraphQL for modern development
- Authentication via access tokens
- Rate limits apply to all APIs
`
  };

  const lowerQuery = query.toLowerCase();
  if (lowerQuery.includes('product')) return shopifyContext.product;
  if (lowerQuery.includes('order')) return shopifyContext.order;
  return shopifyContext.default;
}

async function askShopifyQuestion(question, apiKey, modelId) {
  console.log(`\n🤔 Question: ${question}`);
  
  const context = getShopifyContext(question);
  
  const messages = [
    {
      role: 'system',
      content: `You are a Shopify development expert. Use the provided Shopify context to give accurate, helpful answers.

Shopify Context:
${context}

Provide practical examples and code snippets when relevant.`
    },
    {
      role: 'user',
      content: question
    }
  ];

  console.log(`🤖 Getting response from ${modelId}...`);
  const response = await queryOpenRouter(messages, apiKey, modelId);
  
  console.log(`\n✅ Answer:\n${response}`);
  return response;
}

function getModelId(modelName) {
  if (!modelName) {
    return config.defaultModel;
  }
  
  // Check if it's a short name from availableModels
  if (config.availableModels[modelName]) {
    return config.availableModels[modelName];
  }
  
  // Check if it's a full model ID
  if (Object.values(config.availableModels).includes(modelName)) {
    return modelName;
  }
  
  console.log(`❌ Unknown model: ${modelName}`);
  console.log('Available models:');
  Object.entries(config.availableModels).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  process.exit(1);
}

async function main() {
  const apiKey = process.env.OPENROUTER_API_KEY;
  
  if (!apiKey) {
    console.error('❌ Please set OPENROUTER_API_KEY environment variable');
    console.log('Get your API key from: https://openrouter.ai/keys');
    console.log('Then run: export OPENROUTER_API_KEY="your_api_key_here"');
    return;
  }

  // Get model from command line argument
  const modelName = process.argv[2];
  const modelId = getModelId(modelName);
  
  console.log('🚀 Shopify + OpenRouter Integration');
  console.log(`🤖 Using model: ${modelId}`);
  if (config.modelDescriptions[modelId]) {
    console.log(`📝 ${config.modelDescriptions[modelId]}`);
  }
  console.log('');

  const questions = [
    "How do I create a product using Shopify's GraphQL API?",
    "What's the difference between Admin API and Storefront API?",
    "Show me how to query orders with GraphQL"
  ];

  try {
    for (const question of questions) {
      await askShopifyQuestion(question, apiKey, modelId);
      console.log('\n' + '='.repeat(60));
      
      // Small delay
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🎉 Demo completed!');
    console.log('\n💡 Try different models:');
    Object.entries(config.availableModels).forEach(([key, value]) => {
      console.log(`  npm run openrouter-enhanced ${key}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main().catch(console.error);
