# 🏢 Shopify Partners OAuth Setup Guide

This guide shows you how to set up the Shopify AI Manager using **Shopify Partners** with proper OAuth authentication.

## 🌟 Why Use Shopify Partners?

- ✅ **Professional approach** - Industry standard for app development
- ✅ **Better security** - OAuth flow instead of private app tokens
- ✅ **Scalable** - Can be installed on multiple stores
- ✅ **App Store ready** - Can be published to Shopify App Store later
- ✅ **Enhanced APIs** - Access to more Shopify features

## 📋 Prerequisites

1. **Shopify Partners Account** (free)
2. **Development store** or existing Shopify store
3. **OpenRouter API Key** for AI functionality

## 🚀 Step-by-Step Setup

### Step 1: Create Shopify Partners Account

1. **Visit Shopify Partners**
   - Go to: https://partners.shopify.com/
   - Click **"Join now"** or **"Sign up"**

2. **Complete registration**
   - Use your business email
   - Fill out company information
   - Select **"Build apps"** as primary interest
   - Verify your email address

### Step 2: Create Your App

1. **Access Partners Dashboard**
   - Log into https://partners.shopify.com/
   - Click **"Apps"** in the navigation

2. **Create new app**
   - Click **"Create app"**
   - Choose **"Public app"**
   - App name: `Shopify AI Manager`
   - Click **"Create app"**

### Step 3: Configure App Settings

1. **Basic app information**
   - **App URL**: `http://localhost:5000` (for development)
   - **Allowed redirection URL(s)**: `http://localhost:5000/auth/callback`
   - **Webhook endpoint**: `http://localhost:5000/webhooks` (optional)

2. **App setup**
   - Go to **"App setup"** tab
   - Configure the URLs above
   - Save changes

### Step 4: Set API Permissions

1. **Protected customer data access**
   - In your app settings, scroll to **"Protected customer data access"**
   - Select these scopes:
     - ✅ `read_products` - Read product information
     - ✅ `write_products` - Update products for SEO
     - ✅ `read_orders` - Analyze order patterns
     - ✅ `read_customers` - Customer segmentation
     - ✅ `read_analytics` - Performance insights
     - ✅ `read_content` - Content optimization
     - ✅ `write_content` - Update store content
     - ✅ `read_themes` - Theme analysis
     - ✅ `write_themes` - Theme optimization

2. **Save permissions**
   - Click **"Save"** to apply the scopes

### Step 5: Get App Credentials

1. **Copy your credentials**
   - In your app dashboard, note down:
     - **API key** (Client ID)
     - **API secret key** (Client Secret)
   - You'll need these for the environment variables

### Step 6: Set Environment Variables

Create a `.env` file in the `shopify_ai_manager` directory:

```bash
# Shopify App Credentials (from Partners dashboard)
SHOPIFY_API_KEY=your_api_key_here
SHOPIFY_API_SECRET=your_api_secret_here

# OpenRouter for AI functionality
OPENROUTER_API_KEY=your_openrouter_key_here

# Flask secret (generate a random string)
SECRET_KEY=your_random_secret_key_here
```

Or set them as environment variables:

```bash
export SHOPIFY_API_KEY="your_api_key_here"
export SHOPIFY_API_SECRET="your_api_secret_here"
export OPENROUTER_API_KEY="your_openrouter_key_here"
export SECRET_KEY="your_random_secret_key_here"
```

### Step 7: Run the OAuth Version

1. **Start the OAuth app**
   ```bash
   cd shopify_ai_manager
   source venv/bin/activate
   python oauth_app.py
   ```

2. **Install on your store**
   - Open: http://localhost:5000/auth/install
   - Enter your store name
   - Click **"Install App"**
   - Authorize the permissions on Shopify
   - You'll be redirected back to the dashboard

## 🔧 Development vs Production

### Development Setup
- Use `http://localhost:5000` for URLs
- Test with development stores
- Use ngrok for external testing if needed

### Production Setup
- Use your actual domain (e.g., `https://yourdomain.com`)
- Update redirect URLs in Partners dashboard
- Use environment variables for secrets
- Consider using a proper session store (Redis, database)

## 🛠️ Troubleshooting

### Common Issues

1. **"App installation failed"**
   - Check that API key/secret are correct
   - Verify redirect URL matches exactly
   - Ensure app is not in draft mode

2. **"Invalid redirect URI"**
   - URL in Partners dashboard must match exactly
   - Include protocol (http:// or https://)
   - No trailing slashes

3. **"Scope not granted"**
   - Check that requested scopes are configured in Partners
   - Some scopes require app review for public apps

4. **"Webhook verification failed"**
   - Ensure SHOPIFY_API_SECRET is set correctly
   - Check webhook URL is accessible

### Testing OAuth Flow

1. **Test installation URL**
   ```
   http://localhost:5000/auth/install?shop=your-store
   ```

2. **Manual OAuth URL** (if needed)
   ```
   https://your-store.myshopify.com/admin/oauth/authorize?client_id=YOUR_API_KEY&scope=read_products,write_products&redirect_uri=http://localhost:5000/auth/callback
   ```

## 🔒 Security Best Practices

1. **Environment Variables**
   - Never commit API secrets to version control
   - Use different credentials for development/production

2. **HTTPS in Production**
   - Always use HTTPS for production apps
   - Shopify requires HTTPS for app store apps

3. **State Parameter**
   - OAuth flow includes state parameter for CSRF protection
   - Automatically handled by the OAuth implementation

4. **Webhook Verification**
   - All webhooks are verified using HMAC
   - Prevents unauthorized webhook calls

## 📊 Advantages Over Private Apps

| Feature | Private App | Partners OAuth App |
|---------|-------------|-------------------|
| **Security** | API token | OAuth flow |
| **Scalability** | Single store | Multiple stores |
| **Distribution** | Manual setup | App store ready |
| **Permissions** | All or nothing | Granular scopes |
| **Revocation** | Delete app | OAuth revocation |
| **Webhooks** | Limited | Full webhook support |

## 🎯 Next Steps

After successful OAuth setup:

1. **Test the connection** - Verify data access works
2. **Configure webhooks** - Real-time updates from Shopify
3. **Add error handling** - Robust error management
4. **Implement logging** - Track app usage and issues
5. **Consider app store** - Publish for other merchants

## 🚀 Going Live

To make your app available to other stores:

1. **Update URLs** to production domain
2. **Submit for app review** (if using protected scopes)
3. **Create app listing** in Partners dashboard
4. **Test thoroughly** with multiple stores
5. **Submit to App Store** (optional)

The OAuth approach gives you a professional, scalable foundation for your Shopify AI Manager! 🎉
