nohup: ignoring input
🚀 Starting Shopify AI Manager (OAuth Version)...
==================================================
✅ OAuth credentials configured
✅ OpenRouter API key configured

🌐 Application URLs:
  Dashboard: http://localhost:5000
  Install App: http://localhost:5000/auth/install

🛑 Press Ctrl+C to stop
==================================================
 * Serving Flask app 'oauth_app'
 * Debug mode: on
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
[33mPress CTRL+C to quit[0m
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 115-020-056
127.0.0.1 - - [03/Jun/2025 02:36:20] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:36:20] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:36:42] "[32mGET /auth/install?shop=9ighxj-ir.myshopify.com HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 02:36:54] "[32mGET /auth/install?shop=9ighxj-ir.myshopify.com HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 02:57:12] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:57:12] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:57:12] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:57:13] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:57:19] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 02:57:20] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:57:20] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:57:25] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 02:57:45] "[31m[1mGET /auth/callback HTTP/1.1[0m" 400 -
127.0.0.1 - - [03/Jun/2025 02:57:46] "[31m[1mGET /auth/callback HTTP/1.1[0m" 400 -
127.0.0.1 - - [03/Jun/2025 02:58:17] "[31m[1mGET /auth/callback HTTP/1.1[0m" 400 -
127.0.0.1 - - [03/Jun/2025 02:58:18] "[31m[1mGET /auth/callback HTTP/1.1[0m" 400 -
127.0.0.1 - - [03/Jun/2025 02:59:05] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:59:05] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:59:05] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:59:05] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:59:07] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 02:59:07] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:59:07] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 02:59:10] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:00:44] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:45] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:45] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:45] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:45] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:46] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:46] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:46] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:47] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:47] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:00:47] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:48] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:00:50] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:02:31] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:02:31] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:02:31] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:02:32] "GET / HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:02:33] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:02:33] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:02:34] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:02:36] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:03:01] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:03:01] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:03:05] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:10:36] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:10:36] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:10:37] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:10:39] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:12:49] "[32mGET /auth/install?shop=9ighxj-ir.myshopify.com**** HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:12:52] "[32mGET /auth/install?shop=9ighxj-ir.myshopify.com**** HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:13:03] "[32mGET /auth/install?shop=9ighxj-ir.myshopify.com HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:24:09] "[32mGET /auth/install?shop=9ighxj-ir.myshopify.com HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:25:21] "GET /?hmac=1a10efbde9fb054173479f29b4a5c0b9dd8bff318519ab84e7bc56f13aa01179&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935522 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:21] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:22] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:23] "GET /?hmac=1a10efbde9fb054173479f29b4a5c0b9dd8bff318519ab84e7bc56f13aa01179&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935522 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:28] "GET /?hmac=1a10efbde9fb054173479f29b4a5c0b9dd8bff318519ab84e7bc56f13aa01179&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935522 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:29] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:29] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:29] "GET /?hmac=1a10efbde9fb054173479f29b4a5c0b9dd8bff318519ab84e7bc56f13aa01179&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935522 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:33] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:25:33] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:34] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:37] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:25:56] "GET /?hmac=bbda09279b7211d043a5b27aef0d9b6121b832a3b32d1ca1f44a1c7a69f29b43&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935556 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:56] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:56] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:25:56] "GET /?hmac=bbda09279b7211d043a5b27aef0d9b6121b832a3b32d1ca1f44a1c7a69f29b43&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935556 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:26:01] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:26:02] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:26:02] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:26:08] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:27:37] "[32mGET /auth/install?shop=baddogoptics.com HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:28:39] "[32mGET /auth/install?shop=bad-dog-optics-dev-store.myshopify.com HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:29:06] "GET /?hmac=94952a2c98eaa5fcd81e709e6b32478fcdda71cfafae115ace7b1edf92e320ab&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935746 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:06] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:06] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:07] "GET /?hmac=94952a2c98eaa5fcd81e709e6b32478fcdda71cfafae115ace7b1edf92e320ab&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935746 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:21] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:29:21] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:22] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:24] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:29:37] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:38] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:40] "[32mGET /auth/install?shop=bad-dog-optics-dev-store HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:29:44] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:44] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:50] "GET /?hmac=5f06459448c14e190b69a7c3ae5255f9937dbb14f111e8a41eb26c363e3f7065&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935791 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:50] "GET /?hmac=5f06459448c14e190b69a7c3ae5255f9937dbb14f111e8a41eb26c363e3f7065&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935791 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:50] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:29:50] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:30:30] "GET /?hmac=05b4e4e0cc3915c85011409dc31904b767c8023134904e53cd8ac1202cfa611e&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935831 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:30:30] "GET /api/store-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:30:30] "GET /api/mcp-status HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:30:31] "GET /?hmac=05b4e4e0cc3915c85011409dc31904b767c8023134904e53cd8ac1202cfa611e&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935831 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:30:31] "[32mGET /connect HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:30:31] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:30:33] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:30:36] "[32mGET /auth/install?shop=9ighxj-ir HTTP/1.1[0m" 302 -
127.0.0.1 - - [03/Jun/2025 03:33:26] "GET /auth/install HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:33:26] "GET /?hmac=05b4e4e0cc3915c85011409dc31904b767c8023134904e53cd8ac1202cfa611e&host=YWRtaW4uc2hvcGlmeS5jb20vc3RvcmUvYmFkLWRvZy1vcHRpY3MtZGV2LXN0b3Jl&shop=bad-dog-optics-dev-store.myshopify.com&timestamp=1748935831 HTTP/1.1" 200 -
127.0.0.1 - - [03/Jun/2025 03:44:48] "[32mHEAD /auth/install?shop=9ighxj-ir.myshopify.com HTTP/1.1[0m" 302 -
 * Detected change in '/mnt/c/Users/<USER>/Shopify MCP/dev-mcp/shopify_ai_manager/oauth_app.py', reloading
🚀 Starting Shopify AI Manager (OAuth Version)...
==================================================
✅ OAuth credentials configured
✅ OpenRouter API key configured

🌐 Application URLs:
  Dashboard: http://localhost:5000
  Install App: http://localhost:5000/auth/install

🛑 Press Ctrl+C to stop
==================================================
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 115-020-056
 * Detected change in '/mnt/c/Users/<USER>/Shopify MCP/dev-mcp/shopify_ai_manager/oauth_app.py', reloading
🚀 Starting Shopify AI Manager (OAuth Version)...
==================================================
✅ OAuth credentials configured
✅ OpenRouter API key configured

🌐 Application URLs:
  Dashboard: http://localhost:5000
  Install App: http://localhost:5000/auth/install

🛑 Press Ctrl+C to stop
==================================================
 * Restarting with stat
