<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Shopify AI Manager{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --shopify-green: #00b341;
            --shopify-dark: #004c23;
            --sidebar-width: 250px;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--shopify-green), var(--shopify-dark));
            color: white;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar .logo {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar .logo h3 {
            margin: 0;
            font-weight: bold;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border: none;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-left: 4px solid white;
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        .main-content {
            margin-left: var(--sidebar-width);
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e9ecef;
        }
        
        .content-area {
            padding: 2rem;
        }
        
        .connection-status {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .connection-status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .connection-status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .module-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.2s ease;
        }
        
        .module-card:hover {
            transform: translateY(-2px);
        }
        
        .chat-container {
            height: 400px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            background: white;
        }
        
        .chat-messages {
            height: 300px;
            overflow-y: auto;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .chat-input {
            padding: 1rem;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 10px;
            max-width: 80%;
        }
        
        .message.user {
            background-color: var(--shopify-green);
            color: white;
            margin-left: auto;
        }
        
        .message.ai {
            background-color: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }
        
        .btn-shopify {
            background-color: var(--shopify-green);
            border-color: var(--shopify-green);
            color: white;
        }
        
        .btn-shopify:hover {
            background-color: var(--shopify-dark);
            border-color: var(--shopify-dark);
            color: white;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="logo">
            <h3><i class="fas fa-store"></i> Shopify AI</h3>
            <small>Manager</small>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('connect') }}">
                    <i class="fas fa-plug"></i> Connect Store
                </a>
            </li>
            <hr style="margin: 1rem 0; border-color: rgba(255,255,255,0.1);">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('seo_module') }}">
                    <i class="fas fa-search"></i> SEO
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('ads_module') }}">
                    <i class="fas fa-bullhorn"></i> Ads
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('email_module') }}">
                    <i class="fas fa-envelope"></i> Email Marketing
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('support_module') }}">
                    <i class="fas fa-headset"></i> Customer Support
                </a>
            </li>
        </ul>
        
        <div style="position: absolute; bottom: 1rem; left: 1rem; right: 1rem;">
            <div id="connection-indicator" class="connection-status disconnected text-center">
                <i class="fas fa-circle"></i> Not Connected
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">{% block page_title %}Dashboard{% endblock %}</h4>
                <small class="text-muted">{% block page_subtitle %}Manage your Shopify store with AI{% endblock %}</small>
            </div>
            <div>
                <span id="store-status" class="connection-status disconnected">
                    <i class="fas fa-store"></i> No Store Connected
                </span>
            </div>
        </div>
        
        <!-- Content Area -->
        <div class="content-area">
            {% block content %}{% endblock %}
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global JavaScript functions
        
        // Update connection status
        function updateConnectionStatus() {
            fetch('/api/store-status')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.getElementById('connection-indicator');
                    const storeStatus = document.getElementById('store-status');
                    
                    if (data.connected) {
                        indicator.className = 'connection-status connected text-center';
                        indicator.innerHTML = '<i class="fas fa-circle"></i> Connected';
                        storeStatus.className = 'connection-status connected';
                        storeStatus.innerHTML = `<i class="fas fa-store"></i> ${data.store_url}`;
                    } else {
                        indicator.className = 'connection-status disconnected text-center';
                        indicator.innerHTML = '<i class="fas fa-circle"></i> Not Connected';
                        storeStatus.className = 'connection-status disconnected';
                        storeStatus.innerHTML = '<i class="fas fa-store"></i> No Store Connected';
                    }
                })
                .catch(error => console.error('Error checking connection status:', error));
        }
        
        // AI Chat function
        function sendAIMessage(message, module, chatContainer) {
            const messagesDiv = chatContainer.querySelector('.chat-messages');
            
            // Add user message
            const userMessage = document.createElement('div');
            userMessage.className = 'message user';
            userMessage.textContent = message;
            messagesDiv.appendChild(userMessage);
            
            // Add loading indicator
            const loadingMessage = document.createElement('div');
            loadingMessage.className = 'message ai';
            loadingMessage.innerHTML = '<i class="fas fa-spinner fa-spin"></i> AI is thinking...';
            messagesDiv.appendChild(loadingMessage);
            
            // Scroll to bottom
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            // Send to AI
            fetch('/api/ai-chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    module: module
                })
            })
            .then(response => response.json())
            .then(data => {
                // Remove loading message
                messagesDiv.removeChild(loadingMessage);
                
                // Add AI response
                const aiMessage = document.createElement('div');
                aiMessage.className = 'message ai';
                aiMessage.innerHTML = data.response.replace(/\n/g, '<br>');
                messagesDiv.appendChild(aiMessage);
                
                // Scroll to bottom
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            })
            .catch(error => {
                // Remove loading message
                messagesDiv.removeChild(loadingMessage);
                
                // Add error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'message ai';
                errorMessage.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error: ' + error.message;
                messagesDiv.appendChild(errorMessage);
                
                console.error('Error:', error);
            });
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateConnectionStatus();
            
            // Update connection status every 30 seconds
            setInterval(updateConnectionStatus, 30000);
            
            // Highlight active nav link
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
