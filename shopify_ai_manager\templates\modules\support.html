{% extends "base.html" %}

{% block title %}Customer Support - Shopify AI Manager{% endblock %}

{% block page_title %}Customer Support & Chatbot{% endblock %}
{% block page_subtitle %}Enhance customer service with AI-powered support{% endblock %}

{% block content %}
<div class="row">
    <!-- Support Chat Assistant -->
    <div class="col-lg-8 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-robot"></i> Customer Support AI Assistant
                </h5>
            </div>
            <div class="card-body">
                <div class="chat-container">
                    <div class="chat-messages" id="support-chat-messages">
                        <div class="message ai">
                            <i class="fas fa-headset"></i> 
                            <strong>Support Assistant:</strong> Hello! I'm your customer support specialist. I can help you with:
                            <ul class="mt-2 mb-0">
                                <li>Chatbot setup and training</li>
                                <li>Customer service response templates</li>
                                <li>Support ticket management</li>
                                <li>FAQ creation and optimization</li>
                                <li>Live chat integration</li>
                                <li>Customer satisfaction improvement</li>
                                <li>Support automation workflows</li>
                            </ul>
                            How can I help improve your customer support today?
                        </div>
                    </div>
                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control" id="support-chat-input" 
                                   placeholder="Ask about customer support strategies...">
                            <button class="btn btn-shopify" type="button" onclick="sendSupportMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Support Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Support Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="quickSupportAction('chatbot')">
                        <i class="fas fa-robot"></i> Setup Chatbot
                    </button>
                    <button class="btn btn-outline-success" onclick="quickSupportAction('templates')">
                        <i class="fas fa-file-alt"></i> Response Templates
                    </button>
                    <button class="btn btn-outline-info" onclick="quickSupportAction('faq')">
                        <i class="fas fa-question-circle"></i> Create FAQ
                    </button>
                    <button class="btn btn-outline-warning" onclick="quickSupportAction('analyze')">
                        <i class="fas fa-chart-line"></i> Analyze Tickets
                    </button>
                    <button class="btn btn-outline-secondary" onclick="quickSupportAction('automation')">
                        <i class="fas fa-cogs"></i> Automate Support
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Support Stats -->
        <div class="card module-card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Support Metrics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-primary mb-1">Open Tickets</h6>
                            <h4 class="mb-0">12</h4>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-success mb-1">Avg Response</h6>
                            <h4 class="mb-0">2.3h</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-info mb-1">Satisfaction</h6>
                            <h4 class="mb-0">4.7/5</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-2">
                            <h6 class="text-warning mb-1">Resolution</h6>
                            <h4 class="mb-0">94%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Chatbot Builder -->
    <div class="col-12 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-robot"></i> AI Chatbot Builder
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Chatbot Configuration</h6>
                        <div class="mb-3">
                            <label class="form-label">Chatbot Purpose</label>
                            <select class="form-select" id="chatbot-purpose">
                                <option value="general">General Support</option>
                                <option value="sales">Sales Assistant</option>
                                <option value="technical">Technical Support</option>
                                <option value="order">Order Inquiries</option>
                                <option value="product">Product Information</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Personality</label>
                            <select class="form-select" id="chatbot-personality">
                                <option value="friendly">Friendly & Helpful</option>
                                <option value="professional">Professional</option>
                                <option value="casual">Casual & Fun</option>
                                <option value="expert">Expert & Knowledgeable</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Response Style</label>
                            <select class="form-select" id="chatbot-style">
                                <option value="concise">Concise & Direct</option>
                                <option value="detailed">Detailed & Thorough</option>
                                <option value="conversational">Conversational</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Integration Settings</h6>
                        <div class="mb-3">
                            <label class="form-label">Trigger Conditions</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="trigger-time" checked>
                                <label class="form-check-label" for="trigger-time">
                                    After 30 seconds on page
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="trigger-exit">
                                <label class="form-check-label" for="trigger-exit">
                                    On exit intent
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="trigger-cart">
                                <label class="form-check-label" for="trigger-cart">
                                    When cart is abandoned
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Operating Hours</label>
                            <select class="form-select" id="chatbot-hours">
                                <option value="24/7">24/7 Availability</option>
                                <option value="business">Business Hours Only</option>
                                <option value="custom">Custom Schedule</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Escalation</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="escalate-human" checked>
                                <label class="form-check-label" for="escalate-human">
                                    Escalate to human when needed
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <button class="btn btn-shopify" onclick="generateChatbot()">
                        <i class="fas fa-robot"></i> Generate Chatbot with AI
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Support Tickets -->
    <div class="col-md-8 mb-4">
        <div class="card module-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-ticket-alt"></i> Recent Support Tickets
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="generateResponse()">
                    <i class="fas fa-magic"></i> AI Response
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ticket ID</th>
                                <th>Customer</th>
                                <th>Subject</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#2847</td>
                                <td>
                                    <strong>Sarah Johnson</strong><br>
                                    <small class="text-muted"><EMAIL></small>
                                </td>
                                <td>Order delivery issue</td>
                                <td><span class="badge bg-warning">Medium</span></td>
                                <td><span class="badge bg-info">In Progress</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewTicket('2847')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>#2846</td>
                                <td>
                                    <strong>Mike Chen</strong><br>
                                    <small class="text-muted"><EMAIL></small>
                                </td>
                                <td>Product return request</td>
                                <td><span class="badge bg-success">Low</span></td>
                                <td><span class="badge bg-warning">Pending</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewTicket('2846')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>#2845</td>
                                <td>
                                    <strong>Emma Davis</strong><br>
                                    <small class="text-muted"><EMAIL></small>
                                </td>
                                <td>Payment processing error</td>
                                <td><span class="badge bg-danger">High</span></td>
                                <td><span class="badge bg-success">Resolved</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="viewTicket('2845')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- FAQ Management -->
    <div class="col-md-4 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle"></i> FAQ Management
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Add New FAQ</label>
                    <input type="text" class="form-control mb-2" placeholder="Question" id="faq-question">
                    <textarea class="form-control mb-2" rows="3" placeholder="Answer" id="faq-answer"></textarea>
                    <button class="btn btn-sm btn-outline-primary w-100" onclick="addFAQ()">
                        <i class="fas fa-plus"></i> Add FAQ
                    </button>
                </div>
                
                <div class="mb-3">
                    <h6>Popular FAQs</h6>
                    <div class="list-group list-group-flush">
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div>
                                <strong>Shipping times?</strong><br>
                                <small class="text-muted">Asked 23 times</small>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="editFAQ('shipping')">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div>
                                <strong>Return policy?</strong><br>
                                <small class="text-muted">Asked 18 times</small>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="editFAQ('returns')">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div>
                                <strong>Size guide?</strong><br>
                                <small class="text-muted">Asked 15 times</small>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="editFAQ('sizing')">
                                <i class="fas fa-edit"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-outline-success btn-sm w-100" onclick="generateFAQs()">
                    <i class="fas fa-magic"></i> AI Generate FAQs
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Chatbot Preview Modal -->
<div class="modal fade" id="chatbotModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">AI-Generated Chatbot</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="chatbotContent">
                <!-- Generated chatbot content will appear here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-outline-primary">Test Chatbot</button>
                <button type="button" class="btn btn-shopify">Deploy Chatbot</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Send Support message
    function sendSupportMessage() {
        const input = document.getElementById('support-chat-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        const chatContainer = document.querySelector('.chat-container');
        sendAIMessage(message, 'support', chatContainer);
        
        input.value = '';
    }
    
    // Handle Enter key in chat input
    document.getElementById('support-chat-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendSupportMessage();
        }
    });
    
    // Quick support actions
    function quickSupportAction(action) {
        const prompts = {
            'chatbot': 'Help me set up an AI chatbot for my Shopify store. Provide configuration recommendations and conversation flows.',
            'templates': 'Create professional customer service response templates for common inquiries like shipping, returns, and product questions.',
            'faq': 'Generate a comprehensive FAQ section for my Shopify store covering common customer questions and concerns.',
            'analyze': 'Analyze my customer support tickets and identify patterns, common issues, and improvement opportunities.',
            'automation': 'Set up automated customer support workflows to handle routine inquiries and escalate complex issues.'
        };
        
        const input = document.getElementById('support-chat-input');
        input.value = prompts[action];
        sendSupportMessage();
    }
    
    // Generate chatbot with AI
    function generateChatbot() {
        const purpose = document.getElementById('chatbot-purpose').value;
        const personality = document.getElementById('chatbot-personality').value;
        const style = document.getElementById('chatbot-style').value;
        const hours = document.getElementById('chatbot-hours').value;
        
        const modal = new bootstrap.Modal(document.getElementById('chatbotModal'));
        const content = document.getElementById('chatbotContent');
        
        // Show loading
        content.innerHTML = `
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">AI is creating your chatbot...</p>
            </div>
        `;
        
        modal.show();
        
        // Generate chatbot with AI
        const prompt = `Create a comprehensive chatbot configuration for ${purpose} with ${personality} personality and ${style} response style. 
        Operating hours: ${hours}. Include conversation flows, response templates, and integration instructions.`;
        
        fetch('/api/ai-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: prompt,
                module: 'support'
            })
        })
        .then(response => response.json())
        .then(data => {
            content.innerHTML = `
                <div class="chatbot-result">
                    <h6>Generated Chatbot Configuration:</h6>
                    <div class="border rounded p-3 bg-light">
                        ${data.response.replace(/\n/g, '<br>')}
                    </div>
                </div>
            `;
        })
        .catch(error => {
            content.innerHTML = `<div class="alert alert-danger">Error generating chatbot: ${error.message}</div>`;
        });
    }
    
    // View ticket
    function viewTicket(ticketId) {
        const input = document.getElementById('support-chat-input');
        input.value = `Help me respond to support ticket #${ticketId}. Provide a professional and helpful response.`;
        sendSupportMessage();
    }
    
    // Generate AI response for tickets
    function generateResponse() {
        const input = document.getElementById('support-chat-input');
        input.value = 'Generate professional response templates for the most common customer support scenarios in my store.';
        sendSupportMessage();
    }
    
    // Add FAQ
    function addFAQ() {
        const question = document.getElementById('faq-question').value.trim();
        const answer = document.getElementById('faq-answer').value.trim();
        
        if (!question || !answer) {
            alert('Please enter both question and answer');
            return;
        }
        
        // In a real implementation, this would save to the backend
        alert('FAQ added successfully!');
        
        // Clear inputs
        document.getElementById('faq-question').value = '';
        document.getElementById('faq-answer').value = '';
    }
    
    // Edit FAQ
    function editFAQ(type) {
        const input = document.getElementById('support-chat-input');
        input.value = `Help me improve the FAQ answer for ${type}. Make it more comprehensive and customer-friendly.`;
        sendSupportMessage();
    }
    
    // Generate FAQs with AI
    function generateFAQs() {
        const input = document.getElementById('support-chat-input');
        input.value = 'Generate a comprehensive FAQ section for my Shopify store. Include questions about shipping, returns, payments, products, and customer service.';
        sendSupportMessage();
    }
    
    // Initialize Support module
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Support module initialized');
    });
</script>
{% endblock %}
