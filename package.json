{"name": "@shopify/dev-mcp", "version": "1.1.0", "main": "dist/index.js", "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "build:watch": "tsc --watch", "changeset": "changeset", "test": "vitest run", "test:watch": "vitest", "lint:fix": "prettier --write --ignore-unknown .", "lint": "prettier --check --cache --ignore-unknown .", "inspector": "npm run build && npm exec @modelcontextprotocol/inspector dist/index.js", "typecheck": "tsc --noEmit", "api": "node shopify_mcp_api.js", "example": "node openrouter_usage_example.js", "openrouter": "node simple_openrouter.cjs", "compare-models": "node model_comparison.cjs", "openrouter-enhanced": "node openrouter_enhanced.cjs", "openrouter-env": "node openrouter_with_env.cjs"}, "author": "", "license": "ISC", "description": "A command line tool for setting up Shopify Dev MCP server", "dependencies": {"@modelcontextprotocol/sdk": "^1.6.1", "cors": "^2.8.5", "express": "^4.21.2", "node-fetch": "^3.3.2", "zod": "^3.24.2"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.4", "@jest/globals": "^29.7.0", "@modelcontextprotocol/inspector": "^0.5.1", "@types/node": "^22.13.10", "@vitest/coverage-v8": "^3.0.9", "memfs": "^4.17.0", "prettier": "^3.5.3", "typescript": "^5.8.2", "vitest": "^3.0.9"}, "type": "module", "bin": {"shopify-dev-mcp": "dist/index.js"}, "files": ["dist/**/*.js", "!dist/**/*.test.*", "data/**/*.json.gz", "LICENSE", "README.md", "package.json"], "keywords": ["mcp", "modelcontextprotocol", "shopify"], "prettier": {"endOfLine": "auto"}, "private": false, "publishConfig": {"access": "public", "@shopify:registry": "https://registry.npmjs.org"}}