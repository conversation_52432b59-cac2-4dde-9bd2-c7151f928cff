#!/usr/bin/env python3
"""
Shopify AI Manager - Startup Script
"""

import os
import sys
import subprocess
import time

def check_requirements():
    """Check if all requirements are installed"""
    try:
        import flask
        import requests
        print("✅ Python dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing Python dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_node():
    """Check if Node.js is available"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js is available: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js is not available")
            return False
    except FileNotFoundError:
        print("❌ Node.js is not installed")
        return False

def check_mcp_server():
    """Check if MCP server is built"""
    mcp_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dist', 'index.js')
    if os.path.exists(mcp_path):
        print(f"✅ MCP server found at: {mcp_path}")
        return True
    else:
        print(f"❌ MCP server not found at: {mcp_path}")
        print("Please build the MCP server first: npm run build")
        return False

def check_environment():
    """Check environment variables"""
    openrouter_key = os.environ.get('OPENROUTER_API_KEY')
    if openrouter_key:
        print("✅ OPENROUTER_API_KEY is set")
    else:
        print("⚠️  OPENROUTER_API_KEY is not set")
        print("Set it with: export OPENROUTER_API_KEY='your_api_key_here'")
    
    return True

def main():
    """Main startup function"""
    print("🚀 Starting Shopify AI Manager...")
    print("=" * 50)
    
    # Check all requirements
    checks = [
        check_requirements(),
        check_node(),
        check_mcp_server(),
        check_environment()
    ]
    
    if not all(checks[:3]):  # First 3 are critical
        print("\n❌ Critical requirements not met. Please fix the issues above.")
        sys.exit(1)
    
    print("\n✅ All checks passed!")
    print("🌐 Starting Flask application...")
    print("📱 Open http://localhost:5000 in your browser")
    print("🛑 Press Ctrl+C to stop")
    print("=" * 50)
    
    # Start the Flask application
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down Shopify AI Manager...")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
