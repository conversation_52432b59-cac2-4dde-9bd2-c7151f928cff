<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Shopify AI Manager</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --shopify-green: #00b341;
            --shopify-dark: #004c23;
        }
        
        body {
            background: linear-gradient(135deg, var(--shopify-green), var(--shopify-dark));
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .install-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        
        .install-header {
            background: linear-gradient(135deg, var(--shopify-green), var(--shopify-dark));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .btn-shopify {
            background-color: var(--shopify-green);
            border-color: var(--shopify-green);
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-shopify:hover {
            background-color: var(--shopify-dark);
            border-color: var(--shopify-dark);
            color: white;
            transform: translateY(-2px);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li i {
            color: var(--shopify-green);
            margin-right: 0.75rem;
            width: 20px;
        }
        
        .shop-input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
            transition: border-color 0.3s ease;
        }
        
        .shop-input:focus {
            border-color: var(--shopify-green);
            box-shadow: 0 0 0 0.2rem rgba(0, 179, 65, 0.25);
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 10px 0 0 10px;
        }
        
        .permissions-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .permission-item i {
            color: var(--shopify-green);
            margin-right: 0.5rem;
            width: 16px;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-card">
            <div class="install-header">
                <h1><i class="fas fa-robot"></i> Shopify AI Manager</h1>
                <p class="mb-0">AI-powered store management and optimization</p>
            </div>
            
            <div class="install-body">
                <div class="text-center mb-4">
                    <h3>Install on Your Store</h3>
                    <p class="text-muted">Connect your Shopify store to start using AI-powered management tools</p>
                </div>
                
                <form id="installForm">
                    <div class="mb-4">
                        <label for="shopDomain" class="form-label">
                            <i class="fas fa-store"></i> Your Shopify Store
                        </label>
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control shop-input" 
                                   id="shopDomain" 
                                   placeholder="your-store"
                                   required>
                            <span class="input-group-text">.myshopify.com</span>
                        </div>
                        <div class="form-text">
                            Enter your store name (e.g., if your store is "mystore.myshopify.com", enter "mystore")
                        </div>
                    </div>
                    
                    <div class="permissions-section">
                        <h6><i class="fas fa-shield-alt"></i> App Permissions</h6>
                        <p class="small text-muted mb-3">This app will request the following permissions:</p>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="permission-item">
                                    <i class="fas fa-eye"></i>
                                    <span class="small">Read products</span>
                                </div>
                                <div class="permission-item">
                                    <i class="fas fa-eye"></i>
                                    <span class="small">Read orders</span>
                                </div>
                                <div class="permission-item">
                                    <i class="fas fa-eye"></i>
                                    <span class="small">Read customers</span>
                                </div>
                                <div class="permission-item">
                                    <i class="fas fa-eye"></i>
                                    <span class="small">Read analytics</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="permission-item">
                                    <i class="fas fa-edit"></i>
                                    <span class="small">Update products</span>
                                </div>
                                <div class="permission-item">
                                    <i class="fas fa-edit"></i>
                                    <span class="small">Update content</span>
                                </div>
                                <div class="permission-item">
                                    <i class="fas fa-edit"></i>
                                    <span class="small">Marketing events</span>
                                </div>
                                <div class="permission-item">
                                    <i class="fas fa-edit"></i>
                                    <span class="small">Theme updates</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-shopify btn-lg">
                            <i class="fas fa-download"></i> Install App
                        </button>
                    </div>
                </form>
                
                <div class="mt-4">
                    <h6><i class="fas fa-star"></i> What you'll get:</h6>
                    <ul class="feature-list">
                        <li>
                            <i class="fas fa-search"></i>
                            <span>AI-powered SEO optimization</span>
                        </li>
                        <li>
                            <i class="fas fa-bullhorn"></i>
                            <span>Smart advertising campaign creation</span>
                        </li>
                        <li>
                            <i class="fas fa-envelope"></i>
                            <span>Automated email marketing</span>
                        </li>
                        <li>
                            <i class="fas fa-headset"></i>
                            <span>AI customer support chatbots</span>
                        </li>
                        <li>
                            <i class="fas fa-chart-line"></i>
                            <span>Performance analytics and insights</span>
                        </li>
                    </ul>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        <i class="fas fa-lock"></i> 
                        Secure OAuth authentication • No passwords stored • Revoke access anytime
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('installForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const shopDomain = document.getElementById('shopDomain').value.trim();
            
            if (!shopDomain) {
                alert('Please enter your store name');
                return;
            }
            
            // Validate shop domain format
            if (shopDomain.includes('.') || shopDomain.includes('/')) {
                alert('Please enter only your store name (e.g., "mystore" not "mystore.myshopify.com")');
                return;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
            submitBtn.disabled = true;
            
            // Redirect to OAuth flow
            window.location.href = `/auth/install?shop=${encodeURIComponent(shopDomain)}`;
        });
        
        // Auto-focus on shop input
        document.getElementById('shopDomain').focus();
        
        // Handle Enter key
        document.getElementById('shopDomain').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('installForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
